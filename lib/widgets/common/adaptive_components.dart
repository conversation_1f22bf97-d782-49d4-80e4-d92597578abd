import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../../config/platform_adaptations.dart';
import '../../config/adaptive_icons.dart';
import '../../config/design_system.dart';

// =====================================================
// ADAPTIVE DIALOGS
// =====================================================

/// Platform-appropriate dialog implementations
class AdaptiveDialogs {
  /// Shows platform-appropriate alert dialog
  static Future<T?> showAlert<T>({
    required BuildContext context,
    String? title,
    String? content,
    List<AdaptiveDialogAction>? actions,
    bool barrierDismissible = true,
  }) {
    if (PlatformAdaptations.isIOS) {
      return showCupertinoDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => CupertinoAlertDialog(
          title: title != null ? Text(title) : null,
          content: content != null ? Text(content) : null,
          actions:
              actions?.map((action) => action.toCupertinoAction()).toList() ??
                  [],
        ),
      );
    }

    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AlertDialog(
        title: title != null ? Text(title) : null,
        content: content != null ? Text(content) : null,
        actions:
            actions?.map((action) => action.toMaterialAction()).toList() ?? [],
      ),
    );
  }

  /// Shows platform-appropriate confirmation dialog
  static Future<bool> showConfirmation({
    required BuildContext context,
    String? title,
    String? content,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool isDestructive = false,
  }) async {
    final result = await showAlert<bool>(
      context: context,
      title: title,
      content: content,
      actions: [
        AdaptiveDialogAction(
          text: cancelText,
          onPressed: () => Navigator.pop(context, false),
        ),
        AdaptiveDialogAction(
          text: confirmText,
          onPressed: () => Navigator.pop(context, true),
          isDestructive: isDestructive,
          isDefaultAction: true,
        ),
      ],
    );
    return result ?? false;
  }

  /// Shows platform-appropriate loading dialog
  static void showLoading({
    required BuildContext context,
    String? message,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              PlatformAdaptations.getAdaptiveLoadingIndicator(),
              if (message != null) ...[
                const SizedBox(width: 16),
                Expanded(child: Text(message)),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Represents an action in an adaptive dialog
class AdaptiveDialogAction {
  const AdaptiveDialogAction({
    required this.text,
    required this.onPressed,
    this.isDefaultAction = false,
    this.isDestructive = false,
  });

  final String text;
  final VoidCallback onPressed;
  final bool isDefaultAction;
  final bool isDestructive;

  Widget toCupertinoAction() {
    return CupertinoDialogAction(
      onPressed: onPressed,
      isDefaultAction: isDefaultAction,
      isDestructiveAction: isDestructive,
      child: Text(text),
    );
  }

  Widget toMaterialAction() {
    return TextButton(
      onPressed: onPressed,
      style: isDestructive
          ? TextButton.styleFrom(
              foregroundColor: Colors.red,
            )
          : null,
      child: Text(text),
    );
  }
}

// =====================================================
// ADAPTIVE BUTTONS
// =====================================================

/// Platform-appropriate button widget
class AdaptiveButton extends StatelessWidget {
  const AdaptiveButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.style,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    if (PlatformAdaptations.shouldUseHapticFeedback && onPressed != null) {
      return _HapticButton(
        onPressed: onPressed!,
        child: _buildButton(context),
      );
    }
    return _buildButton(context);
  }

  Widget _buildButton(BuildContext context) {
    final adaptiveStyle =
        style ?? PlatformAdaptations.getAdaptiveButtonStyle(context);

    return ElevatedButton(
      onPressed: onPressed,
      style: adaptiveStyle.copyWith(
        backgroundColor: backgroundColor != null
            ? WidgetStateProperty.all(backgroundColor)
            : null,
        foregroundColor: foregroundColor != null
            ? WidgetStateProperty.all(foregroundColor)
            : null,
        padding: padding != null ? WidgetStateProperty.all(padding) : null,
        shape: borderRadius != null
            ? WidgetStateProperty.all(
                RoundedRectangleBorder(borderRadius: borderRadius!),
              )
            : null,
      ),
      child: child,
    );
  }
}

/// Wrapper that adds haptic feedback to buttons
class _HapticButton extends StatelessWidget {
  const _HapticButton({
    required this.onPressed,
    required this.child,
  });

  final VoidCallback onPressed;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      child: child,
    );
  }
}

// =====================================================
// ADAPTIVE LIST TILES
// =====================================================

/// Platform-appropriate list tile widget
class AdaptiveListTile extends StatelessWidget {
  const AdaptiveListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.selected = false,
    this.enabled = true,
    this.contentPadding,
  });

  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool selected;
  final bool enabled;
  final EdgeInsetsGeometry? contentPadding;

  @override
  Widget build(BuildContext context) {
    final adaptiveTrailing =
        trailing ?? (onTap != null ? Icon(AdaptiveIcons.chevronRight) : null);

    return ListTile(
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: adaptiveTrailing,
      onTap: onTap != null && PlatformAdaptations.shouldUseHapticFeedback
          ? () {
              HapticFeedback.lightImpact();
              onTap!();
            }
          : onTap,
      selected: selected,
      enabled: enabled,
      contentPadding: contentPadding ??
          const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceM,
            vertical: DesignSystem.spaceXS,
          ),
      minTileHeight: PlatformAdaptations.adaptiveListItemHeight,
      shape: RoundedRectangleBorder(
        borderRadius:
            BorderRadius.circular(PlatformAdaptations.adaptiveBorderRadius),
      ),
    );
  }
}

// =====================================================
// ADAPTIVE CARDS
// =====================================================

/// Platform-appropriate card widget
class AdaptiveCard extends StatelessWidget {
  const AdaptiveCard({
    super.key,
    required this.child,
    this.margin,
    this.padding,
    this.onTap,
    this.elevation,
    this.color,
  });

  final Widget child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  final double? elevation;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final decoration = PlatformAdaptations.getAdaptiveCardDecoration(context);

    Widget card = Container(
      margin: margin,
      padding: padding ?? DesignSystem.cardContentPadding,
      decoration: decoration.copyWith(
        color: color ?? decoration.color,
      ),
      child: child,
    );

    if (onTap != null) {
      card = InkWell(
        onTap: PlatformAdaptations.shouldUseHapticFeedback
            ? () {
                HapticFeedback.lightImpact();
                onTap!();
              }
            : onTap,
        borderRadius:
            BorderRadius.circular(PlatformAdaptations.adaptiveBorderRadius),
        child: card,
      );
    }

    return card;
  }
}

// =====================================================
// ADAPTIVE BOTTOM SHEET
// =====================================================

/// Platform-appropriate bottom sheet
class AdaptiveBottomSheet {
  /// Shows platform-appropriate bottom sheet
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    if (PlatformAdaptations.isIOS) {
      return showCupertinoModalPopup<T>(
        context: context,
        builder: (context) => CupertinoActionSheet(
          actions: [child],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),
      );
    }

    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(PlatformAdaptations.adaptiveBorderRadius),
        ),
      ),
      builder: (context) => child,
    );
  }
}
