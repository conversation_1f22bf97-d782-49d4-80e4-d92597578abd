import 'dart:math';
import 'package:dasso_reader/models/hsk_character.dart';
import 'package:dasso_reader/models/java_metrics_learn.dart';

// Import the MyPair class from JavaMetricsLearn
class MyPair {
  final int key;
  final int value;

  MyPair(this.key, this.value);
}

/// This class adapts the Java-style learning algorithm to work with your existing Flutter implementation
class JavaLearnAdapter {
  final JavaMetricsLearn _metricsLearn = JavaMetricsLearn();
  final List<HskCharacter> _characters;
  List<int> _positionShuffle = [];

  JavaLearnAdapter(this._characters) {
    // Initialize the metrics learn with the number of characters
    _metricsLearn.init();
  }

  /// Get the current stage (1-9)
  int get currentStage => _metricsLearn.stageVar;

  /// Get the display type (0 = Chinese prompt, 1 = Pinyin/English prompt, 2 = special)
  int get displayType => _metricsLearn.itemDisplayType;

  /// Get the number of answer options to display
  int get numberOfChoices => _metricsLearn.itemDisplayNumber;

  /// Check if the learning session is finished
  bool get isFinished => _metricsLearn.FINISHED;

  /// Check if we're in the final stage (8 or 9)
  bool get isFinalStage => _metricsLearn.stageVar >= 8;

  /// Get the count of learned items for reset logic testing
  int get learnedItemsCount => _metricsLearn.learnItemsInt;

  /// Test if we should reset at stage 4 (matching Java logic)
  bool shouldResetAtStageFour() {
    if (_metricsLearn.stageVar != 4) return false;

    // In Java, this depends on stageFourBool and is set to false after first reset
    final shouldReset = _metricsLearn.stageFourBool;

    // Debug info
    print(
      'Stage 4 reset check: $shouldReset (learnItems: ${_metricsLearn.learnItemsInt})',
    );

    return shouldReset;
  }

  /// Test if we should reset at stage 7 (matching Java logic)
  bool shouldResetAtStageSeven() {
    // In Java, this depends on learnItemsInt, stageSevenBool1, and stageSevenBool2
    if (_metricsLearn.stageVar != 7) return false;

    // Complex reset logic from Java version
    bool shouldContinue = _metricsLearn.learnItemsInt > 27 ||
        (_metricsLearn.learnItemsInt > 22 && !_metricsLearn.stageSevenBool2) ||
        (_metricsLearn.learnItemsInt > 14 && !_metricsLearn.stageSevenBool1);

    // Debug info with more details
    print('Stage 7 reset check: ${!shouldContinue}');
    print('  - learnItems: ${_metricsLearn.learnItemsInt}');
    print('  - stageSevenBool1: ${_metricsLearn.stageSevenBool1}');
    print('  - stageSevenBool2: ${_metricsLearn.stageSevenBool2}');

    return !shouldContinue;
  }

  /// Check if we've learned enough for stage 8
  bool readyForStageFinal() {
    if (_metricsLearn.stageVar != 8) return false;

    final isReady = _metricsLearn.learnItemsInt >= 30;
    print(
      'Stage 8 readiness check: $isReady (${_metricsLearn.learnItemsInt}/30 items)',
    );

    return isReady;
  }

  /// Reset logic diagnostic info
  Map<String, dynamic> getResetLogicState() {
    return {
      'stage': _metricsLearn.stageVar,
      'learnItemsCount': _metricsLearn.learnItemsInt,
      'stageFourBool': _metricsLearn.stageFourBool,
      'stageSevenBool1': _metricsLearn.stageSevenBool1,
      'stageSevenBool2': _metricsLearn.stageSevenBool2,
      'stageLength': _metricsLearn.stageLength,
      'stageCount': _metricsLearn.stageCount,
    };
  }

  /// Get detailed status of current learning session
  Map<String, dynamic> getLearningStatus() {
    final status = {
      'currentStage': _metricsLearn.stageVar,
      'displayType': _metricsLearn.itemDisplayType,
      'itemsCount': _metricsLearn.itemDisplayNumber,
      'learnedItems': _metricsLearn.learnItemsInt,
      'finished': _metricsLearn.FINISHED,
      'stageProgress':
          '${_metricsLearn.stageCount}/${_metricsLearn.stageLength}',
    };

    return status;
  }

  /// Start a new round and get the choices to display
  List<HskCharacter> startNewRound() {
    // Get the indices for this round
    List<int> indices = _metricsLearn.newRound();

    // Map indices to characters
    List<HskCharacter> choices = [];
    for (int i = 0;
        i < indices.length && i < _metricsLearn.itemDisplayNumber;
        i++) {
      int characterIndex = indices[i];
      if (characterIndex < _characters.length) {
        choices.add(_characters[characterIndex]);
      }
    }

    // Make sure we have enough choices
    while (choices.length < _metricsLearn.itemDisplayNumber &&
        _characters.length > choices.length) {
      // Add random characters as fillers
      int randomIndex = Random().nextInt(_characters.length);
      if (!choices
          .any((c) => c.characterId == _characters[randomIndex].characterId)) {
        choices.add(_characters[randomIndex]);
      }
    }

    // Set up the position shuffle
    _positionShuffle = _metricsLearn.setPositionShuffle(_positionShuffle);

    // Reorder choices based on position shuffle
    List<HskCharacter?> shuffledChoices =
        List<HskCharacter?>.filled(_metricsLearn.itemDisplayNumber, null);

    // Place each choice in its shuffled position
    for (int i = 0; i < _positionShuffle.length && i < choices.length; i++) {
      int targetPosition = _positionShuffle.indexOf(i);
      if (targetPosition >= 0 && targetPosition < shuffledChoices.length) {
        shuffledChoices[targetPosition] = choices[i];
      }
    }

    // Convert to non-nullable list (remove any null values)
    List<HskCharacter> result = [];
    for (HskCharacter? character in shuffledChoices) {
      if (character != null) {
        result.add(character);
      }
    }

    // If we somehow ended up with an empty list, return the original choices
    if (result.isEmpty && choices.isNotEmpty) {
      return choices;
    }

    return result;
  }

  /// Get the current character (the one to learn)
  HskCharacter getCurrentCharacter() {
    int index = _metricsLearn.curItemInd;
    if (index < _characters.length) {
      return _characters[index];
    }
    // Fallback
    return _characters[0];
  }

  /// Get the position shuffle (for determining which button is correct)
  List<int> getPositionShuffle() {
    return _positionShuffle;
  }

  /// Get the correct answer index
  int getCorrectAnswerIndex() {
    // The correct answer is at the position indicated by the position shuffle
    return _positionShuffle.indexOf(0);
  }

  /// Check if we should save progress now
  bool checkSaveNow() {
    return _metricsLearn.checkSaveNow();
  }

  /// Get the pairs to save
  List<MyPair> getSaveNowPairs() {
    // Convert from JavaMetricsLearn.MyPair to our MyPair
    List<dynamic> originalPairs = _metricsLearn.getSaveNowPairs();
    List<MyPair> convertedPairs = [];

    for (var pair in originalPairs) {
      convertedPairs.add(MyPair(pair.returnKey(), pair.returnValue()));
    }

    return convertedPairs;
  }

  /// Handle a correct answer
  void handleCorrectAnswer() {
    _metricsLearn.updateStats();
  }

  /// Handle a wrong answer
  void handleWrongAnswer() {
    _metricsLearn.missedFlag = true;
  }

  /// Save the current progress
  Map<String, dynamic> saveProgress() {
    return {
      'stageVar': _metricsLearn.stageVar,
      'stageCount': _metricsLearn.stageCount,
      'levels':
          _metricsLearn.learnItemsList.map((item) => item.curLevel).toList(),
    };
  }

  /// Restore progress from saved data
  void restoreProgress(Map<String, dynamic> data) {
    int stageVar = data['stageVar'] ?? 1;
    int stageCount = data['stageCount'] ?? 0;
    List<int> levels = List<int>.from(data['levels'] ?? List.filled(30, 15));

    _metricsLearn.reinit(stageVar, levels);
    _metricsLearn.setRestartStageCount(stageCount);
  }
}
