import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'efficient_data_handling.dart';

/// Unified efficient data handling utilities for Dasso Reader
class EfficientDataUtils {
  static final MemoryLeakDetector _memoryDetector = MemoryLeakDetector();
  static final Map<String, PaginationManager> _paginationManagers = {};
  static final Map<String, IntelligentCache> _caches = {};
  static bool _isInitialized = false;

  /// Initialize the efficient data handling system
  static void initialize() {
    if (_isInitialized) return;

    _memoryDetector.startMonitoring();
    _isInitialized = true;

    if (kDebugMode) {
      debugPrint('🚀 EfficientDataUtils: Initialized');
    }
  }

  /// Create or get a pagination manager for a specific data type
  static PaginationManager<T> getPaginationManager<T>({
    required String key,
    required DataType dataType,
    required Future<PaginatedResult<T>> Function(int page, int pageSize)
        dataLoader,
    PaginationConfig? customConfig,
  }) {
    if (_paginationManagers.containsKey(key)) {
      return _paginationManagers[key] as PaginationManager<T>;
    }

    final config = customConfig ?? PaginationConfig.forDataType(dataType);
    final manager = PaginationManager<T>(
      cacheKey: key,
      config: config,
      dataLoader: dataLoader,
    );

    _paginationManagers[key] = manager;
    _memoryDetector.trackResource(key, 'PaginationManager');

    if (kDebugMode) {
      debugPrint('📄 EfficientDataUtils: Created PaginationManager for $key');
    }

    return manager;
  }

  /// Create or get an intelligent cache for a specific purpose
  static IntelligentCache<K, V> getCache<K, V>({
    required String name,
    int maxSize = 100,
    Duration defaultTtl = const Duration(minutes: 30),
  }) {
    if (_caches.containsKey(name)) {
      return _caches[name] as IntelligentCache<K, V>;
    }

    final cache = IntelligentCache<K, V>(
      name: name,
      maxSize: maxSize,
      defaultTtl: defaultTtl,
    );

    _caches[name] = cache;
    _memoryDetector.trackResource(name, 'IntelligentCache');

    if (kDebugMode) {
      debugPrint('💾 EfficientDataUtils: Created IntelligentCache: $name');
    }

    return cache;
  }

  /// Transform data efficiently based on size
  static Future<List<T>> transformData<T>({
    required List<dynamic> data,
    required T Function(dynamic) transform,
    String? taskId,
  }) async {
    return await DataTransformationOptimizer.optimizedTransform<T>(
      data: data,
      transform: transform,
      taskId: taskId,
    );
  }

  /// Transform data with frame-aware processing
  static Future<List<T>> transformDataFrameAware<T>({
    required List<dynamic> data,
    required T Function(dynamic) transform,
    int chunkSize = 10,
  }) async {
    return await DataTransformationOptimizer.frameAwareTransform<T>(
      data: data,
      transform: transform,
      chunkSize: chunkSize,
    );
  }

  /// Transform data with debouncing for frequently changing data
  static Future<List<T>> transformDataDebounced<T>({
    required List<dynamic> data,
    required T Function(dynamic) transform,
    Duration delay = const Duration(milliseconds: 300),
  }) async {
    return await DataTransformationOptimizer.debouncedTransform<T>(
      data: data,
      transform: transform,
      delay: delay,
    );
  }

  /// Track a resource for memory leak detection
  static void trackResource(
    String id,
    String type, {
    Map<String, dynamic>? metadata,
  }) {
    _memoryDetector.trackResource(id, type, metadata: metadata);
  }

  /// Untrack a resource when properly disposed
  static void untrackResource(String id) {
    _memoryDetector.untrackResource(id);
  }

  /// Get comprehensive performance statistics
  static Map<String, dynamic> getPerformanceStatistics() {
    final memoryStats = _memoryDetector.getStatistics();
    final paginationStats = <String, dynamic>{};
    final cacheStats = <String, dynamic>{};

    // Collect pagination manager statistics
    for (final entry in _paginationManagers.entries) {
      paginationStats[entry.key] = {
        'cachedPages': entry.value.cachedPagesCount,
        'estimatedMemoryUsage': entry.value.estimatedMemoryUsage,
      };
    }

    // Collect cache statistics
    for (final entry in _caches.entries) {
      cacheStats[entry.key] = entry.value.getStatistics();
    }

    return {
      'memoryLeakDetection': memoryStats,
      'paginationManagers': paginationStats,
      'caches': cacheStats,
      'backgroundProcessor': {
        'activeTasks': BackgroundProcessor().activeTasksCount,
        'activeTaskIds': BackgroundProcessor().activeTaskIds,
      },
    };
  }

  /// Clear all caches
  static void clearAllCaches() {
    for (final cache in _caches.values) {
      cache.clear();
    }

    for (final manager in _paginationManagers.values) {
      manager.clearCache();
    }

    if (kDebugMode) {
      debugPrint('🧹 EfficientDataUtils: Cleared all caches');
    }
  }

  /// Dispose specific pagination manager
  static void disposePaginationManager(String key) {
    final manager = _paginationManagers.remove(key);
    if (manager != null) {
      manager.dispose();
      _memoryDetector.untrackResource(key);

      if (kDebugMode) {
        debugPrint('📄 EfficientDataUtils: Disposed PaginationManager: $key');
      }
    }
  }

  /// Dispose specific cache
  static void disposeCache(String name) {
    final cache = _caches.remove(name);
    if (cache != null) {
      cache.dispose();
      _memoryDetector.untrackResource(name);

      if (kDebugMode) {
        debugPrint('💾 EfficientDataUtils: Disposed IntelligentCache: $name');
      }
    }
  }

  /// Dispose all resources
  static void dispose() {
    // Dispose all pagination managers
    for (final entry in _paginationManagers.entries) {
      entry.value.dispose();
      _memoryDetector.untrackResource(entry.key);
    }
    _paginationManagers.clear();

    // Dispose all caches
    for (final entry in _caches.entries) {
      entry.value.dispose();
      _memoryDetector.untrackResource(entry.key);
    }
    _caches.clear();

    // Stop memory leak detection
    _memoryDetector.stopMonitoring();
    _memoryDetector.clearTracking();

    // Dispose background processor
    BackgroundProcessor().dispose();

    _isInitialized = false;

    if (kDebugMode) {
      debugPrint('🚀 EfficientDataUtils: Disposed all resources');
    }
  }
}

/// Mixin for widgets that need efficient data handling with automatic cleanup
mixin EfficientDataMixin<T extends StatefulWidget> on State<T> {
  final List<String> _trackedResources = [];
  final List<String> _paginationManagers = [];
  final List<String> _caches = [];

  /// Track a resource for automatic cleanup
  void trackResource(String id, String type, {Map<String, dynamic>? metadata}) {
    EfficientDataUtils.trackResource(id, type, metadata: metadata);
    _trackedResources.add(id);
  }

  /// Create a pagination manager with automatic cleanup
  PaginationManager<U> createPaginationManager<U>({
    required String key,
    required DataType dataType,
    required Future<PaginatedResult<U>> Function(int page, int pageSize)
        dataLoader,
    PaginationConfig? customConfig,
  }) {
    final manager = EfficientDataUtils.getPaginationManager<U>(
      key: key,
      dataType: dataType,
      dataLoader: dataLoader,
      customConfig: customConfig,
    );
    _paginationManagers.add(key);
    return manager;
  }

  /// Create a cache with automatic cleanup
  IntelligentCache<K, V> createCache<K, V>({
    required String name,
    int maxSize = 100,
    Duration defaultTtl = const Duration(minutes: 30),
  }) {
    final cache = EfficientDataUtils.getCache<K, V>(
      name: name,
      maxSize: maxSize,
      defaultTtl: defaultTtl,
    );
    _caches.add(name);
    return cache;
  }

  @override
  void dispose() {
    // Cleanup tracked resources
    for (final id in _trackedResources) {
      EfficientDataUtils.untrackResource(id);
    }

    // Cleanup pagination managers
    for (final key in _paginationManagers) {
      EfficientDataUtils.disposePaginationManager(key);
    }

    // Cleanup caches
    for (final name in _caches) {
      EfficientDataUtils.disposeCache(name);
    }

    super.dispose();
  }
}

/// Extension methods for common data operations
extension EfficientDataExtensions on List {
  /// Transform list efficiently based on size
  Future<List<T>> transformEfficiently<T>(T Function(dynamic) transform) async {
    return await EfficientDataUtils.transformData<T>(
      data: this,
      transform: transform,
    );
  }

  /// Transform list with frame-aware processing
  Future<List<T>> transformFrameAware<T>(T Function(dynamic) transform) async {
    return await EfficientDataUtils.transformDataFrameAware<T>(
      data: this,
      transform: transform,
    );
  }

  /// Transform list with debouncing
  Future<List<T>> transformDebounced<T>(T Function(dynamic) transform) async {
    return await EfficientDataUtils.transformDataDebounced<T>(
      data: this,
      transform: transform,
    );
  }
}
