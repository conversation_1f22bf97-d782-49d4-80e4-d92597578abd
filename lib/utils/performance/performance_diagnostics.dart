import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/performance_optimizer.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';

/// Professional performance diagnostics tool for Dasso Reader
class PerformanceDiagnostics {
  static final PerformanceDiagnostics _instance =
      PerformanceDiagnostics._internal();
  factory PerformanceDiagnostics() => _instance;
  PerformanceDiagnostics._internal();

  /// Run comprehensive performance diagnostics
  Future<Map<String, dynamic>> runDiagnostics() async {
    AnxLog.info('🔍 Starting Performance Diagnostics...');

    final diagnostics = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': Platform.operatingSystem,
      'buildMode':
          kDebugMode ? 'debug' : (kProfileMode ? 'profile' : 'release'),
      'systemStatus': await _checkSystemStatus(),
      'performanceMetrics': await _getPerformanceMetrics(),
      'jankAnalysis': await _analyzeJankPatterns(),
      'memoryAnalysis': await _analyzeMemoryUsage(),
      'optimizationEffectiveness': await _checkOptimizationEffectiveness(),
      'recommendations': await _generateRecommendations(),
    };

    _logDiagnosticsSummary(diagnostics);
    return diagnostics;
  }

  /// Check if performance system is properly initialized and running
  Future<Map<String, dynamic>> _checkSystemStatus() async {
    final performanceMetrics = PerformanceMetrics();
    final performanceOptimizer = PerformanceOptimizer();

    // Get diagnostic info from optimizer
    final optimizerInfo = performanceOptimizer.getDiagnosticInfo();
    final systemStatus = optimizerInfo['systemStatus'] as Map<String, dynamic>;

    return {
      'performanceMetricsInitialized': performanceMetrics.isInitialized,
      'performanceOptimizerInitialized': systemStatus['isInitialized'] ?? false,
      'optimizationTimerActive':
          systemStatus['optimizationTimerActive'] ?? false,
      'monitoringActive': performanceMetrics.isMonitoring,
    };
  }

  /// Get current performance metrics
  Future<Map<String, dynamic>> _getPerformanceMetrics() async {
    final report = PerformanceMetrics().getPerformanceReport();

    return {
      'overallScore': report.overallScore,
      'frameRate': {
        'current': report.frameRateMetrics.currentFPS,
        'average': report.frameRateMetrics.averageFPS,
        'target': report.frameRateMetrics.targetFPS,
        'jankRate': report.frameRateMetrics.jankRate,
        'jankCount': report.frameRateMetrics.jankCount,
        'totalFrames': report.frameRateMetrics.totalFrames,
      },
      'memory': {
        'current': report.memoryMetrics.currentUsageMB,
        'peak': report.memoryMetrics.peakUsageMB,
        'usagePercent': report.memoryMetrics.usagePercent,
        'isMemoryPressure': report.memoryMetrics.isMemoryPressure,
        'memorySpikes': report.memoryMetrics.memorySpikes.length,
      },
      'performanceGrade': report.performanceGrade,
    };
  }

  /// Analyze jank patterns to identify root causes
  Future<Map<String, dynamic>> _analyzeJankPatterns() async {
    final report = PerformanceMetrics().getPerformanceReport();
    final jankEvents = report.frameRateMetrics.jankEvents;

    if (jankEvents.isEmpty) {
      return {
        'status': 'No jank detected',
        'patterns': [],
        'recommendations': ['Performance is good!'],
      };
    }

    // Analyze jank patterns
    final now = DateTime.now();
    final recentJanks = jankEvents
        .where((event) => now.difference(event.timestamp).inMinutes < 10)
        .toList();

    final severeCounts =
        jankEvents.where((e) => e.severity.name == 'severe').length;
    final moderateCounts =
        jankEvents.where((e) => e.severity.name == 'moderate').length;
    final mildCounts =
        jankEvents.where((e) => e.severity.name == 'mild').length;

    final patterns = <String>[];
    if (recentJanks.length > 10) {
      patterns.add(
        'Frequent jank events (${recentJanks.length} in last 10 minutes)',
      );
    }
    if (severeCounts > 5) {
      patterns.add('Multiple severe jank events ($severeCounts total)');
    }
    if (moderateCounts > 15) {
      patterns.add('High moderate jank count ($moderateCounts total)');
    }

    return {
      'totalJankEvents': jankEvents.length,
      'recentJankEvents': recentJanks.length,
      'severityBreakdown': {
        'severe': severeCounts,
        'moderate': moderateCounts,
        'mild': mildCounts,
      },
      'patterns': patterns,
      'averageJankDuration': jankEvents.isNotEmpty
          ? jankEvents
                  .map((e) => e.frameTime.inMilliseconds)
                  .reduce((a, b) => a + b) /
              jankEvents.length
          : 0,
    };
  }

  /// Analyze memory usage patterns
  Future<Map<String, dynamic>> _analyzeMemoryUsage() async {
    final report = PerformanceMetrics().getPerformanceReport();
    final memoryMetrics = report.memoryMetrics;

    final issues = <String>[];
    if (memoryMetrics.isMemoryPressure) {
      issues.add('Memory pressure detected');
    }
    if (memoryMetrics.usagePercent > 80) {
      issues.add(
        'High memory usage (${memoryMetrics.usagePercent.toStringAsFixed(1)}%)',
      );
    }
    if (memoryMetrics.memorySpikes.length > 10) {
      issues.add(
        'Frequent memory spikes (${memoryMetrics.memorySpikes.length} total)',
      );
    }

    return {
      'currentUsage': memoryMetrics.currentUsageMB,
      'peakUsage': memoryMetrics.peakUsageMB,
      'usagePercent': memoryMetrics.usagePercent,
      'memorySpikes': memoryMetrics.memorySpikes.length,
      'issues': issues,
      'efficiency': memoryMetrics.performanceScore,
    };
  }

  /// Check if optimizations are being applied effectively
  Future<Map<String, dynamic>> _checkOptimizationEffectiveness() async {
    final diagnosticInfo = PerformanceOptimizer().getDiagnosticInfo();
    final recentActivity =
        diagnosticInfo['recentActivity'] as Map<String, dynamic>;

    final effectiveness = <String, dynamic>{
      'systemActive': diagnosticInfo['systemStatus']['optimizationTimerActive'],
      'currentIssues': recentActivity['currentIssues'],
      'performanceScore': recentActivity['performanceScore'],
      'recentJankEvents': recentActivity['jankEventsLast5Min'],
      'recentMemorySpikes': recentActivity['memorySpikesLast5Min'],
    };

    // Determine if optimizations are working
    final score = recentActivity['performanceScore'] as double;
    final isEffective = score > 70.0 && recentActivity['currentIssues'] == 0;

    effectiveness['isEffective'] = isEffective;
    effectiveness['status'] = isEffective
        ? 'Optimizations working well'
        : 'Optimizations may need adjustment';

    return effectiveness;
  }

  /// Generate recommendations based on analysis
  Future<List<String>> _generateRecommendations() async {
    final recommendations = <String>[];
    final report = PerformanceMetrics().getPerformanceReport();

    // Frame rate recommendations
    if (report.frameRateMetrics.jankRate > 20) {
      recommendations.add(
        '🎯 High jank rate detected. Consider reducing widget complexity or using const constructors.',
      );
    }
    if (report.frameRateMetrics.currentFPS < 45) {
      recommendations.add(
        '📱 Low FPS detected. Check for heavy computations on the main thread.',
      );
    }

    // Memory recommendations
    if (report.memoryMetrics.isMemoryPressure) {
      recommendations.add(
        '🧠 Memory pressure detected. Consider implementing more aggressive caching cleanup.',
      );
    }
    if (report.memoryMetrics.memorySpikes.length > 10) {
      recommendations.add(
        '📈 Frequent memory spikes. Check for memory leaks or large object allocations.',
      );
    }

    // System recommendations
    if (!PerformanceMetrics().isMonitoring) {
      recommendations.add(
        '⚠️ Performance monitoring is not active. Check initialization in main.dart.',
      );
    }

    if (recommendations.isEmpty) {
      recommendations
          .add('✅ Performance looks good! Keep up the optimizations.');
    }

    return recommendations;
  }

  /// Log comprehensive diagnostics summary
  void _logDiagnosticsSummary(Map<String, dynamic> diagnostics) {
    AnxLog.info('📊 PERFORMANCE DIAGNOSTICS SUMMARY');
    AnxLog.info('=====================================');

    final systemStatus = diagnostics['systemStatus'] as Map<String, dynamic>;
    AnxLog.info('🔧 System Status:');
    AnxLog.info(
      '  Performance Metrics: ${systemStatus['performanceMetricsInitialized'] ? "✅ Active" : "❌ Inactive"}',
    );
    AnxLog.info(
      '  Performance Optimizer: ${systemStatus['performanceOptimizerInitialized'] ? "✅ Active" : "❌ Inactive"}',
    );
    AnxLog.info(
      '  Optimization Timer: ${systemStatus['optimizationTimerActive'] ? "✅ Running" : "❌ Stopped"}',
    );

    final metrics = diagnostics['performanceMetrics'] as Map<String, dynamic>;
    AnxLog.info('📈 Current Performance:');
    AnxLog.info(
      '  Overall Score: ${(metrics['overallScore'] as double).toStringAsFixed(1)}/100',
    );
    AnxLog.info(
      '  FPS: ${(metrics['frameRate']['current'] as double).toStringAsFixed(1)} (Target: ${metrics['frameRate']['target']})',
    );
    AnxLog.info(
      '  Jank Rate: ${(metrics['frameRate']['jankRate'] as double).toStringAsFixed(1)}%',
    );
    AnxLog.info(
      '  Memory: ${metrics['memory']['current']}MB (${(metrics['memory']['usagePercent'] as double).toStringAsFixed(1)}%)',
    );

    final recommendations = diagnostics['recommendations'] as List<String>;
    AnxLog.info('💡 Recommendations:');
    for (final rec in recommendations) {
      AnxLog.info('  $rec');
    }

    AnxLog.info('=====================================');
  }

  /// Quick health check - returns simple status
  String getQuickHealthCheck() {
    final report = PerformanceMetrics().getPerformanceReport();
    final score = report.overallScore;

    if (score >= 90) return '🟢 Excellent Performance';
    if (score >= 80) return '🟡 Good Performance';
    if (score >= 70) return '🟠 Fair Performance';
    if (score >= 60) return '🔴 Poor Performance';
    return '🚨 Critical Performance Issues';
  }
}
